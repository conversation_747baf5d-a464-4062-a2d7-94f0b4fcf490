# Automated Distance Measurement Tool

A sophisticated computer vision tool that combines AI object detection with automated clicking to measure distances between objects in screenshots. Fully integrated with the existing captcha solver infrastructure for maximum accuracy and reliability.

## 🎯 Features

- **Automated Object Detection**: Uses Grok Vision API to identify and locate objects
- **Precise Coordinate Calculation**: Calculates exact center points of detected objects
- **Automated Clicking**: Uses pyautogui to automatically click on detected objects
- **Distance Calculation**: Computes pixel distance between clicked points
- **Safety Features**: Built-in validation and failsafe mechanisms
- **Integration Ready**: Seamlessly works with existing captcha solver infrastructure
- **Continuous Monitoring**: Support for repeated measurements over time
- **Detailed Logging**: Comprehensive measurement reports and logs

## 🚀 Quick Start

### Basic Usage

```bash
# Single automated measurement
python automated_distance_tool.py --mode auto --objects "fox,robot"

# Continuous monitoring every 10 seconds
python automated_distance_tool.py --mode monitor --interval 10 --objects "fox,robot"

# With logging enabled
python automated_distance_tool.py --mode auto --objects "fox,robot" --log
```

### Integration with Captcha Solver

```python
from automated_distance_tool import AutomatedDistanceTool

# Initialize tool
tool = AutomatedDistanceTool()

# Run measurement (analysis only, no clicking)
distance = tool.integrate_with_captcha_solver()
print(f"Distance: {distance:.2f} pixels")

# Full automated measurement with clicking
distance = tool.run_automated_measurement("fox and robot")
print(f"Distance: {distance:.2f} pixels")
```

## 📋 Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `--mode` | Measurement mode: `auto`, `manual`, `monitor` | `auto` |
| `--objects` | Description of objects to detect | `"two main objects"` |
| `--interval` | Seconds between measurements (monitor mode) | `5` |
| `--log` | Save results to log file | `False` |
| `--validate` | Validate detection accuracy | `True` |

## 🔧 Configuration

The tool uses the same configuration as the captcha solver:

```python
# API Configuration
self.api_key = "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"
self.api_url = "https://openrouter.ai/api/v1"

# Captcha Region (same as captcha solver)
self.captcha_bbox = (492, 381, 826, 645)  # (left, top, right, bottom)
```

## 🎮 Testing

### Run Comprehensive Tests

```bash
# Run all automated tests
python test_distance_tool.py

# Interactive testing mode
python test_distance_tool.py --interactive
```

### Test Scenarios

1. **Basic Measurement**: Full automated measurement with clicking
2. **Integration Mode**: Analysis-only mode for captcha solver integration
3. **Validation Features**: Coordinate validation and safety checks
4. **Measurement Comparison**: Consistency testing with different descriptions

## 📊 Output Examples

### Successful Measurement
```
🚀 Starting Automated Distance Measurement Process
============================================================
📸 Step 1: Capturing screenshot...
📸 Screenshot captured: (492, 381, 826, 645)
📏 Screenshot size: (334, 264)
💡 Screenshot average brightness: 243.2 (0=black, 255=white)

🤖 Step 2: Detecting objects with Grok Vision...
📡 Response status: 200
✅ Detected fox: {'x': 92, 'y': 142} - Orange fox character
✅ Detected robot: {'x': 172, 'y': 142} - Blue robot character
🎯 Detection confidence: high

🖱️  Step 3: Clicking on detected objects...
🎯 Clicking on fox (Orange fox character) at (584, 523)
✅ Successfully clicked on fox
🎯 Clicking on robot (Blue robot character) at (664, 523)
✅ Successfully clicked on robot

📏 Step 4: Calculating distance...
📏 DISTANCE CALCULATION RESULTS:
   Point 1: (584, 523)
   Point 2: (664, 523)
   Distance: 80.00 pixels
   Horizontal gap: 80 pixels
   Vertical gap: 0 pixels

============================================================
🎉 AUTOMATED MEASUREMENT COMPLETED SUCCESSFULLY!
📏 Final Distance: 80.00 pixels
============================================================
```

## 🛡️ Safety Features

### Coordinate Validation
- Ensures detected coordinates are within captcha bounds
- Warns about coordinates near edges (potential detection errors)
- Validates coordinates before clicking

### PyAutoGUI Failsafe
- Failsafe enabled by default (move mouse to top-left corner to abort)
- Screen bounds checking before clicking
- Smooth mouse movements to avoid detection

### Error Handling
- Comprehensive exception handling
- Graceful degradation on API failures
- Detailed error logging and reporting

## 🔗 Integration Methods

### Method 1: Direct Integration
```python
from automated_distance_tool import AutomatedDistanceTool

tool = AutomatedDistanceTool()
distance = tool.integrate_with_captcha_solver()
```

### Method 2: Analysis Only (No Clicking)
```python
# For analysis without affecting the captcha
distance = tool.integrate_with_captcha_solver()
```

### Method 3: Custom Object Detection
```python
# Specify exact objects to detect
distance = tool.run_automated_measurement("orange fox and blue robot")
```

## 📝 Logging and Reports

### Measurement Log Format
```
2025-01-18 15:30:45 - Distance: 80.00 pixels - Objects: ['fox', 'robot']
2025-01-18 15:31:50 - Distance: 82.50 pixels - Objects: ['fox', 'robot']
```

### Detailed Report Structure
```python
{
    "timestamp": "2025-01-18 15:30:45",
    "distance_pixels": 80.00,
    "objects_detected": 2,
    "object_details": {
        "fox": {
            "coordinates": [92, 142],
            "description": "Orange fox character",
            "absolute_coordinates": [584, 523]
        },
        "robot": {
            "coordinates": [172, 142], 
            "description": "Blue robot character",
            "absolute_coordinates": [664, 523]
        }
    },
    "captcha_region": [492, 381, 826, 645],
    "clicked_points": [[584, 523], [664, 523]]
}
```

## 🚨 Troubleshooting

### Common Issues

1. **API Key Error**: Ensure the OpenRouter API key is valid
2. **Screenshot Issues**: Check captcha region coordinates
3. **Detection Failures**: Try different object descriptions
4. **Clicking Errors**: Verify screen coordinates and permissions

### Debug Mode
Enable detailed logging by modifying the tool initialization:
```python
tool = AutomatedDistanceTool()
# Add debug prints in methods as needed
```

## 📦 Dependencies

- `requests`: API communication
- `pyautogui`: Automated clicking
- `PIL`: Screenshot capture
- `json`: Data parsing
- `base64`: Image encoding
- `time`: Timing and delays
- `math`: Distance calculations

## 🤝 Contributing

The tool is designed to be easily extensible. Key areas for enhancement:
- Additional object detection models
- More sophisticated clicking strategies
- Enhanced validation algorithms
- Real-world unit conversion
- GUI interface

## 📄 License

This tool is part of the Captcha Solver project and follows the same licensing terms.
