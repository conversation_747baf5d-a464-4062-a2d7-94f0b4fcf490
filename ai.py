import requests
import json
import base64
import pyautogui
from PIL import ImageGrab
import time

class CaptchaSolver:
    def __init__(self):
        self.api_key = "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"
        self.api_url = "https://openrouter.ai/api/v1"  # Grok API endpoint
        # Store captcha bounding box for coordinate conversion
        self.captcha_bbox = (492, 381, 826, 645)  # (left, top, right, bottom)
        # Precise slide button coordinates from manual testing
        self.slide_button_absolute = (534, 606)  # Average from manual measurements

    def capture_screen_area(self):
        """Capture screenshot of captcha area with debugging info"""
        screenshot = ImageGrab.grab(bbox=self.captcha_bbox)
        screenshot.save("captcha_temp.png")

        # Add debugging info about screenshot
        print(f"Screenshot captured: {self.captcha_bbox}")
        print(f"Screenshot size: {screenshot.size}")

        # Check if screenshot is too dark (average pixel value)
        try:
            import numpy as np
            screenshot_array = np.array(screenshot)
            avg_brightness = np.mean(screenshot_array)
            print(f"Screenshot average brightness: {avg_brightness:.1f} (0=black, 255=white)")

            if avg_brightness < 50:
                print("⚠️  WARNING: Screenshot appears very dark - may cause AI analysis issues")
        except ImportError:
            print("NumPy not available - skipping brightness check")

        return "captcha_temp.png"
    
    def analyze_with_grok(self, image_path):
        """Send image to Grok Vision for initial analysis"""
        with open(image_path, "rb") as image_file:
            base64_image = base64.b64encode(image_file.read()).decode('utf-8')

        payload = {
            "model": "qwen/qwen2.5-vl-32b-instruct",  # Superior vision model for precise object detection
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": """Analyze this slider captcha image with extreme precision. The goal is to drag one object to overlap/align with another object.

CRITICAL REQUIREMENTS FOR OBJECT DETECTION:
- Identify the two main objects in the image
- Measure the precise pixel coordinates of each object's CENTER point
- Look carefully at the actual object boundaries and find the true geometric center
- Consider the object's visual mass distribution when determining center
- Objects are typically positioned in the middle area of the captcha
- The coordinates should be relative to this image (not the full screen)
- Be extremely accurate - even 5-10 pixel errors can cause alignment failure

ALIGNMENT GOAL:
- The task is to drag the source object so it visually overlaps/aligns with the target object
- Consider the final positioning needed for proper visual alignment
- Account for object sizes when determining target coordinates
- Be consistent and precise - small errors in coordinate detection cause captcha failures

Return your response in this exact JSON format:
{
  "objects": [
    {
      "name": "object1_name",
      "coordinates": {"x": pixel_x, "y": pixel_y}
    },
    {
      "name": "object2_name",
      "coordinates": {"x": pixel_x, "y": pixel_y}
    }
  ],
  "move": {
    "from": "source_object_name",
    "to": "target_object_name"
  }
}

Please provide only the JSON response without any additional text."""
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            "temperature": 0.1
        }

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        try:
            response = requests.post(f"{self.api_url}/chat/completions", headers=headers, json=payload)

            # Debug: Print response status and content
            print(f"Response status: {response.status_code}")
            print(f"Response headers: {response.headers}")

            if response.status_code != 200:
                print(f"API Error: {response.status_code}")
                print(f"Response text: {response.text}")
                return None

            # Check if response has content
            if not response.text.strip():
                print("Empty response from API")
                return None

            response_json = response.json()
            print(f"API Response: {response_json}")
            return response_json

        except requests.exceptions.RequestException as e:
            print(f"Request error: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"JSON decode error: {e}")
            print(f"Response text: {response.text}")
            return None





    def analyze_progress_with_grok(self, image_path, current_progress_pct):
        """Send progress screenshot to Grok for mid-drag analysis"""
        try:
            with open(image_path, "rb") as image_file:
                base64_image = base64.b64encode(image_file.read()).decode('utf-8')

            payload = {
                "model": "qwen/qwen2.5-vl-32b-instruct",
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": f"""Analyze this slider captcha progress image. We are at approximately {current_progress_pct:.0f}% of the drag operation.

CRITICAL OBJECT OVERLAP DETECTION (FULL OVERLAP VERSION):
- Look ONLY at the two main objects — IGNORE the slider track completely
- The goal is NOT just touching, but one object must be fully over the other
- "Fully overlap" = the visible area of the source object is centered and mostly covering the target object’s body

OVERLAP DETECTION CRITERIA:
- NOT SUCCESS if the objects are merely touching edges
- NOT SUCCESS if there is only partial overlap between the two objects
- SUCCESS ONLY if the source object’s center aligns with the target object’s center so that the source visually covers the target
- If the source is positioned slightly off-center (target still clearly visible on one side) → CONTINUE dragging

DECISION CRITERIA (STRICT FULL OVERLAP ENFORCEMENT):
- CONTINUE: If there is still a gap between objects, or if they are only touching/partially overlapping
- STOP: Only when the source object is centered over the target, with major overlap (target largely hidden beneath)
- NO EARLY STOPPING: Do not stop at edge contact, adjacency, or partial overlap
- FULL OVERLAP REQUIRED: Alignment must be within ~5–10 pixels of both centers for success

CRITICAL INSTRUCTIONS:
- Be STRICT: touching, edge alignment, or partial overlap is NOT enough
- Require the source object’s center point to align closely with the target object’s center point
- Final stopping point = source object fully covers the target object
- IGNORE slider percentage completely — focus only on visual overlap alignment

Return your response in this exact JSON format:
{{
  "action": "CONTINUE|STOP|ADJUST_DISTANCE",
  "object_alignment_status": "description of current object positioning",
  "slider_completion_estimate": percentage_number,
  "reasoning": "explanation focusing on object alignment and visual completion",
  "distance_adjustment": adjustment_pixels_if_needed
}}

Please provide only the JSON response without any additional text."""
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                "temperature": 0.1
            }

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            response = requests.post(f"{self.api_url}/chat/completions", headers=headers, json=payload)

            if response.status_code != 200:
                print(f"Progress analysis API Error: {response.status_code}")
                return None

            if not response.text.strip():
                print("Empty progress analysis response")
                return None

            return response.json()

        except Exception as e:
            print(f"Progress analysis error: {e}")
            return None




    def analyze_progress_with_proximity(self, image_path, current_progress_pct, object_distance, proximity_mode):
        """Send progress screenshot to Grok with proximity-aware analysis criteria"""
        try:
            with open(image_path, "rb") as image_file:
                base64_image = base64.b64encode(image_file.read()).decode('utf-8')

            # Generate proximity-specific stopping criteria
            if proximity_mode == "ULTRA_AGGRESSIVE":  # ≤30px apart
                stopping_criteria = """
ULTRA-AGGRESSIVE STOPPING CRITERIA (Objects ≤30px apart):
- STOP: If objects are touching, overlapping, or even very close (within 8-12 pixels)
- STOP: If there's ANY visual contact or edge alignment between objects
- STOP: If objects appear to be approaching optimal alignment zone
- STOP: Better to stop early than overshoot with such close objects
- CONTINUE: Only if there's still an obvious gap (>12 pixels) between objects"""
            elif proximity_mode == "AGGRESSIVE":  # ≤50px apart
                stopping_criteria = """
AGGRESSIVE STOPPING CRITERIA (Objects ≤50px apart):
- STOP: If objects are touching, overlapping, or very close (within 10-15 pixels)
- STOP: If there's visual contact or edge alignment between objects
- STOP: If objects appear to be in the alignment zone
- CONTINUE: Only if there's still a clear gap (>15 pixels) between objects"""
            elif proximity_mode == "MODERATE":  # ≤100px apart
                stopping_criteria = """
MODERATE STOPPING CRITERIA (Objects ≤100px apart):
- STOP: If objects are overlapping or touching edges
- STOP: If objects appear properly aligned for captcha success
- CONTINUE: If there's still a gap between objects (>10 pixels)"""
            else:  # Standard mode
                stopping_criteria = """
STANDARD STOPPING CRITERIA (Objects >100px apart):
- CONTINUE: If there is still a gap between objects, or if they are only touching/partially overlapping
- STOP: Only when the source object is centered over the target, with major overlap
- FULL OVERLAP REQUIRED: Alignment must be within ~5–10 pixels of both centers"""

            payload = {
                "model": "qwen/qwen2.5-vl-32b-instruct",
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": f"""Analyze this slider captcha progress image. We are at approximately {current_progress_pct:.0f}% of the drag operation.

OBJECT PROXIMITY CONTEXT:
- Original distance between objects: {object_distance:.1f} pixels
- Proximity mode: {proximity_mode}
- This affects how aggressive we should be with stopping

{stopping_criteria}

CRITICAL OBJECT ANALYSIS:
- Look ONLY at the two main objects — IGNORE the slider track completely
- Focus on the current gap/overlap between the objects
- Consider the proximity mode when making stopping decisions
- For close objects, err on the side of stopping early to prevent overshoot

Return your response in this exact JSON format:
{{
  "action": "CONTINUE|STOP|ADJUST_DISTANCE",
  "object_alignment_status": "description of current object positioning and gap",
  "slider_completion_estimate": percentage_number,
  "reasoning": "explanation focusing on object proximity and alignment",
  "distance_adjustment": adjustment_pixels_if_needed
}}

Please provide only the JSON response without any additional text."""
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                "temperature": 0.1
            }

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            response = requests.post(f"{self.api_url}/chat/completions", headers=headers, json=payload)

            if response.status_code != 200:
                print(f"Proximity analysis API Error: {response.status_code}")
                return None

            if not response.text.strip():
                print("Empty proximity analysis response")
                return None

            return response.json()

        except Exception as e:
            print(f"Proximity analysis error: {e}")
            return None




    def verify_object_alignment(self, image_path, source_obj, target_obj):
        """Verify final object alignment after drag completion"""
        try:
            with open(image_path, "rb") as image_file:
                base64_image = base64.b64encode(image_file.read()).decode('utf-8')

            payload = {
                "model": "qwen/qwen2.5-vl-32b-instruct",
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": f"""Analyze this final captcha state to verify object alignment. The task was to move the {source_obj} to the {target_obj}.

CRITICAL ALIGNMENT VERIFICATION:
- Look at the current positions of the {source_obj} and {target_obj}
- Determine if they are properly aligned/overlapping as required for captcha success
- Check if the {source_obj} appears to be positioned on top of or overlapping with the {target_obj}
- Assess the visual alignment quality - are they properly positioned for success?
- Look for any visual indicators of successful completion (green checkmarks, success messages, etc.)

COMPLETION INDICATORS:
- Check if the slider track appears complete
- Look for success/failure messages
- Assess overall captcha completion status

Return your response in this exact JSON format:
{{
  "alignment_success": true_or_false,
  "alignment_quality": "excellent|good|poor|failed",
  "visual_description": "description of current object positions",
  "completion_indicators": "any success/failure messages or visual cues",
  "recommendation": "assessment of whether captcha should be successful"
}}

Please provide only the JSON response without any additional text."""
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                "temperature": 0.1
            }

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            response = requests.post(f"{self.api_url}/chat/completions", headers=headers, json=payload)

            if response.status_code != 200:
                print(f"Alignment verification API Error: {response.status_code}")
                return None

            if not response.text.strip():
                print("Empty alignment verification response")
                return None

            response_json = response.json()
            content = response_json["choices"][0]["message"]["content"]

            if "```json" in content:
                json_str = content.split("```json")[1].split("```")[0].strip()
            else:
                json_str = content.strip()

            alignment_data = json.loads(json_str)
            return alignment_data

        except Exception as e:
            print(f"Alignment verification error: {e}")
            return None

    def verify_object_alignment_secondary(self, image_path, source_obj, target_obj):
        """Secondary verification using Gemini Pro Vision for consistency check"""
        try:
            with open(image_path, "rb") as image_file:
                base64_image = base64.b64encode(image_file.read()).decode('utf-8')

            payload = {
                "model": "qwen/qwen2.5-vl-32b-instruct",
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": f"""Provide a secondary verification of this captcha completion. The task was to move the {source_obj} to align with the {target_obj}.

CONSISTENCY CHECK REQUIREMENTS:
- Analyze the current positions of both objects with extreme precision
- Determine if they are properly overlapping/aligned for captcha success
- Be conservative in your assessment - only report success if alignment is clearly visible
- Focus on actual visual overlap, not just proximity

Return your response in this exact JSON format:
{{
  "alignment_verified": true_or_false,
  "confidence_level": "high|medium|low",
  "visual_assessment": "detailed description of object positions",
  "success_prediction": "likely_success|likely_failure|uncertain"
}}

Please provide only the JSON response without any additional text."""
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                "temperature": 0.1
            }

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            response = requests.post(f"{self.api_url}/chat/completions", headers=headers, json=payload)

            if response.status_code != 200:
                print(f"Secondary verification API Error: {response.status_code}")
                return None

            response_json = response.json()
            content = response_json["choices"][0]["message"]["content"]

            if "```json" in content:
                json_str = content.split("```json")[1].split("```")[0].strip()
            else:
                json_str = content.strip()

            return json.loads(json_str)

        except Exception as e:
            print(f"Secondary verification error: {e}")
            return None

    def execute_movement(self, analysis_result):
        """Execute the mouse movement based on AI analysis"""
        try:
            # Extract content from AI response
            content = analysis_result["choices"][0]["message"]["content"]
            print(f"AI Response Content: {content}")

            # Check if AI refused to analyze
            if "unable to analyze" in content.lower() or "cannot analyze" in content.lower() or "apologize" in content.lower():
                print("AI refused to analyze the image - attempting fallback approach")
                print("Reason:", content[:200] + "..." if len(content) > 200 else content)

                # Fallback: Use default object positions for common captcha layouts
                print("Using fallback object positions for standard captcha layout")
                fallback_data = {
                    "objects": [
                        {"name": "source_object", "coordinates": {"x": 80, "y": 150}},
                        {"name": "target_object", "coordinates": {"x": 250, "y": 150}}
                    ],
                    "move": {"from": "source_object", "to": "target_object"}
                }

                # Continue with fallback data instead of returning False
                data = fallback_data
                print("Using fallback coordinates: source (80, 150) -> target (250, 150)")
            else:
                # Parse JSON (handle potential markdown formatting)
                if "```json" in content:
                    json_str = content.split("```json")[1].split("```")[0].strip()
                elif "```" in content:
                    # Handle case where JSON is in code blocks without 'json' label
                    json_str = content.split("```")[1].strip()
                else:
                    json_str = content.strip()

                # Try to find JSON in the content if it's mixed with other text
                if not json_str.startswith("{"):
                    # Look for JSON object in the content
                    import re
                    json_match = re.search(r'\{.*\}', content, re.DOTALL)
                    if json_match:
                        json_str = json_match.group()
                    else:
                        print("No JSON found in response")
                        return False

                data = json.loads(json_str)
            
            # Extract coordinates
            objects = {obj["name"]: obj["coordinates"] for obj in data["objects"]}
            from_obj = data["move"]["from"]
            to_obj = data["move"]["to"]

            # Get object positions for distance calculation
            start_pos = objects[from_obj]
            end_pos = objects[to_obj]

            # Validate coordinates are within captcha bounds
            captcha_width = self.captcha_bbox[2] - self.captcha_bbox[0]
            captcha_height = self.captcha_bbox[3] - self.captcha_bbox[1]

            print(f"Captcha dimensions: {captcha_width}x{captcha_height} pixels")
            print(f"Raw AI coordinates - From: ({start_pos['x']}, {start_pos['y']}) To: ({end_pos['x']}, {end_pos['y']})")

            # Clamp coordinates to valid bounds (with 10px margin)
            start_pos['x'] = max(10, min(start_pos['x'], captcha_width - 10))
            start_pos['y'] = max(10, min(start_pos['y'], captcha_height - 10))
            end_pos['x'] = max(10, min(end_pos['x'], captcha_width - 10))
            end_pos['y'] = max(10, min(end_pos['y'], captcha_height - 10))

            print(f"Validated coordinates - From: ({start_pos['x']}, {start_pos['y']}) To: ({end_pos['x']}, {end_pos['y']})")

            # Use precise hardcoded slide button coordinates from manual testing
            print(f"Using precise slide button coordinates: {self.slide_button_absolute}")

            # Convert relative coordinates to absolute screen coordinates
            # The AI returns coordinates relative to the cropped captcha image
            # We need to add the captcha bounding box offset
            captcha_left, captcha_top = self.captcha_bbox[0], self.captcha_bbox[1]

            # Add vertical offset to account for instruction text area
            # The objects are typically below the "Move the X to the Y" text
            text_area_offset = 90  # Adjusted to better target the objects below the text

            # Use precise hardcoded slide button coordinates as starting point
            abs_start_x = self.slide_button_absolute[0]
            abs_start_y = self.slide_button_absolute[1]
            print(f"Using precise slide button coordinates: ({abs_start_x}, {abs_start_y})")

            # Calculate the distance between objects for reference
            distance_x = abs(end_pos['x'] - start_pos['x'])
            distance_y = abs(end_pos['y'] - start_pos['y'])
            total_distance = (distance_x ** 2 + distance_y ** 2) ** 0.5

            # For object alignment captchas, drag to align objects (NOT to complete slider bar)
            # The goal is object overlap, not slider completion percentage

            captcha_width = self.captcha_bbox[2] - self.captcha_bbox[0]  # Total width
            captcha_left = self.captcha_bbox[0]

            # Calculate target position based on object coordinates (not slider completion)
            # Drag the source object to the target object position
            target_object_x = captcha_left + end_pos['x']  # Target object absolute position

            # ADAPTIVE OVERSHOOT CALCULATION BASED ON OBJECT PROXIMITY
            # Close objects need minimal/no overshoot to prevent overshooting
            if total_distance <= 30:  # Very close objects (≤30px apart)
                alignment_overshoot = 0  # No overshoot - stop exactly at target
                proximity_mode = "ULTRA_AGGRESSIVE"
                print("🔥 ULTRA-AGGRESSIVE MODE: Objects ≤30px apart - Zero overshoot")
            elif total_distance <= 50:  # Close objects (≤50px apart)
                alignment_overshoot = 2  # Minimal overshoot
                proximity_mode = "AGGRESSIVE"
                print("⚡ AGGRESSIVE MODE: Objects ≤50px apart - Minimal overshoot")
            elif total_distance <= 100:  # Medium distance
                alignment_overshoot = 5  # Small overshoot
                proximity_mode = "MODERATE"
                print("🎯 MODERATE MODE: Objects ≤100px apart - Small overshoot")
            else:  # Far objects
                alignment_overshoot = 10  # Standard overshoot
                proximity_mode = "STANDARD"
                print("📏 STANDARD MODE: Objects >100px apart - Standard overshoot")

            if end_pos['x'] > start_pos['x']:  # Moving right
                abs_end_x = target_object_x + alignment_overshoot
            else:  # Moving left
                abs_end_x = target_object_x - alignment_overshoot

            print(f"Distance between objects: {total_distance:.1f} pixels")
            print(f"Target object position: x={target_object_x} (relative: {end_pos['x']})")
            print(f"Proximity mode: {proximity_mode}")
            print(f"Alignment overshoot: {alignment_overshoot} pixels")
            print(f"Final target position: x={abs_end_x}")

            # Calculate drag distance (will be adjusted based on AI feedback)
            initial_drag_distance = abs(abs_end_x - abs_start_x)
            print(f"Initial planned drag distance: {initial_drag_distance:.0f} pixels")
            print("NOTE: Actual drag may stop early based on AI object alignment feedback")

            # Fix Y-coordinate drift: Keep drag horizontal by using slide button Y-coordinate
            # Slider captchas should maintain the same Y level throughout the drag
            abs_end_y = self.slide_button_absolute[1]  # Use slide button Y-coordinate
            print(f"Maintaining horizontal drag: Y-coordinate fixed at {abs_end_y}")

            # Perform human-like movement
            print(f"Moving {from_obj} to {to_obj}")
            print(f"Slide button coords: {self.slide_button_absolute}")
            print(f"Object coords - From: ({start_pos['x']}, {start_pos['y']}) To: ({end_pos['x']}, {end_pos['y']})")
            print(f"Absolute coords - From: ({abs_start_x}, {abs_start_y}) To: ({abs_end_x}, {abs_end_y})")

            # Minimal random variations for maximum precision
            import random
            start_x = abs_start_x + random.randint(-1, 1)  # ±1 pixel for precision
            start_y = abs_start_y + random.randint(-1, 1)
            end_x = abs_end_x + random.randint(-1, 1)  # ±1 pixel for precision
            end_y = abs_end_y + random.randint(-1, 1)

            print(f"Final mouse coordinates - Start: ({start_x}, {start_y}) End: ({end_x}, {end_y})")
            print(f"Drag distance: {abs(end_x - start_x)} pixels horizontally")

            # Execute slower, more precise drag with intermediate waypoints
            print("Executing precise drag operation...")
            print(f"Phase 1: Moving to slide button at ({start_x}, {start_y})")
            pyautogui.moveTo(start_x, start_y, duration=0.4)
            time.sleep(0.2)

            # Dynamic waypoint calculation based on proximity analysis
            total_planned_distance = abs(end_x - start_x)
            current_x = start_x

            print(f"Starting dynamic proximity-based drag (total planned: {total_planned_distance:.0f}px)")
            print("Waypoints will be calculated dynamically based on object proximity")

            print("Phase 2: Starting dynamic proximity-based drag")

            # Start drag operation
            pyautogui.mouseDown(button='left')
            time.sleep(0.1)

            # ADAPTIVE WAYPOINT CALCULATION BASED ON OBJECT PROXIMITY
            remaining_distance = total_planned_distance

            # Adjust waypoint distances based on proximity mode
            if proximity_mode == "ULTRA_AGGRESSIVE":  # ≤30px apart
                waypoint_1_percent = 0.20  # Move only 20% initially for ultra-precision
                print("🔥 Ultra-aggressive waypoint: 20% initial movement")
            elif proximity_mode == "AGGRESSIVE":  # ≤50px apart
                waypoint_1_percent = 0.25  # Move 25% initially
                print("⚡ Aggressive waypoint: 25% initial movement")
            elif proximity_mode == "MODERATE":  # ≤100px apart
                waypoint_1_percent = 0.30  # Move 30% initially
                print("🎯 Moderate waypoint: 30% initial movement")
            else:  # Standard mode
                waypoint_1_percent = 0.33  # Standard 33%
                print("📏 Standard waypoint: 33% initial movement")

            waypoint_1_distance = remaining_distance * waypoint_1_percent
            waypoint_1_x = current_x + waypoint_1_distance

            print(f"Dynamic Waypoint 1: Moving {waypoint_1_distance:.0f}px to x={waypoint_1_x:.0f}")
            pyautogui.moveTo(waypoint_1_x, end_y, duration=0.5)
            current_x = waypoint_1_x
            print("Waypoint 1 reached - performing proximity analysis")



            # Capture mid-drag screenshot for AI analysis
            progress_screenshot = ImageGrab.grab(bbox=self.captcha_bbox)
            progress_screenshot.save("progress_33pct.png")

            # AI progress analysis at 33% with proximity awareness
            progress_analysis = self.analyze_progress_with_proximity("progress_33pct.png", 33, total_distance, proximity_mode)
            if progress_analysis:
                try:
                    content = progress_analysis["choices"][0]["message"]["content"]
                    if "```json" in content:
                        json_str = content.split("```json")[1].split("```")[0].strip()
                    else:
                        json_str = content.strip()

                    progress_data = json.loads(json_str)
                    action = progress_data.get("action", "CONTINUE")
                    alignment_status = progress_data.get("object_alignment_status", "Unknown")
                    reasoning = progress_data.get("reasoning", "No reasoning provided")

                    print(f"AI Progress Analysis (33%) - {proximity_mode}: {action}")
                    print(f"Object alignment: {alignment_status}")
                    print(f"Reasoning: {reasoning}")

                    if action == "STOP":
                        print("🎯 AI detected object overlap at Waypoint 1 - Adding small final adjustment")

                        # Add small 10px drag for close objects to ensure proper alignment
                        if total_distance <= 100:  # For close/moderate objects
                            final_adjustment = 10
                            final_x = current_x + final_adjustment
                            print(f"🔧 Adding {final_adjustment}px final adjustment for close objects")
                            pyautogui.moveTo(final_x, end_y, duration=0.3)
                            print(f"📍 Final adjustment completed: moved to {final_x:.0f}px")

                        pyautogui.mouseUp(button='left')
                        print("✅ SUCCESS: Drag stopped with final adjustment")
                        print(f"📍 Final position: {current_x + (final_adjustment if total_distance <= 100 else 0):.0f}px")
                        return True
                    elif action == "ADJUST_DISTANCE":
                        adjustment = progress_data.get("distance_adjustment", 0)
                        print(f"AI recommends distance adjustment: {adjustment} pixels")
                        # Adjust remaining distance dynamically
                        remaining_distance += adjustment
                        end_x += adjustment
                        print(f"Adjusted remaining distance: {remaining_distance:.0f}px")

                    # Dynamic distance adjustment based on proximity
                    if "very close" in alignment_status.lower() or "approaching" in alignment_status.lower():
                        remaining_distance *= 0.5  # Reduce remaining distance by 50%
                        print(f"🔄 Objects very close - reducing remaining distance by 50% to {remaining_distance:.0f}px")
                    elif "close" in alignment_status.lower():
                        remaining_distance *= 0.7  # Reduce remaining distance by 30%
                        print(f"🔄 Objects close - reducing remaining distance by 30% to {remaining_distance:.0f}px")

                except Exception as e:
                    print(f"Error parsing progress analysis at 33%: {e}")

            time.sleep(0.1)

            # ADAPTIVE WAYPOINT 2: Calculate based on proximity mode and remaining distance
            remaining_distance = max(20, remaining_distance)  # Minimum 20px remaining

            # Adjust second waypoint percentage based on proximity mode
            if proximity_mode == "ULTRA_AGGRESSIVE":  # ≤30px apart
                waypoint_2_percent = 0.40  # Move only 40% of remaining distance
                print("🔥 Ultra-aggressive waypoint 2: 40% of remaining distance")
            elif proximity_mode == "AGGRESSIVE":  # ≤50px apart
                waypoint_2_percent = 0.50  # Move 50% of remaining distance
                print("⚡ Aggressive waypoint 2: 50% of remaining distance")
            elif proximity_mode == "MODERATE":  # ≤100px apart
                waypoint_2_percent = 0.55  # Move 55% of remaining distance
                print("🎯 Moderate waypoint 2: 55% of remaining distance")
            else:  # Standard mode
                waypoint_2_percent = 0.60  # Standard 60%
                print("📏 Standard waypoint 2: 60% of remaining distance")

            waypoint_2_distance = remaining_distance * waypoint_2_percent
            waypoint_2_x = current_x + waypoint_2_distance

            # Ensure we don't exceed the original planned endpoint
            waypoint_2_x = min(waypoint_2_x, end_x)

            print(f"Dynamic Waypoint 2: Moving {waypoint_2_distance:.0f}px to x={waypoint_2_x:.0f}")
            pyautogui.moveTo(waypoint_2_x, end_y, duration=0.5)
            current_x = waypoint_2_x
            print("Waypoint 2 reached - performing proximity analysis")

            # Capture mid-drag screenshot for AI analysis
            progress_screenshot = ImageGrab.grab(bbox=self.captcha_bbox)
            progress_screenshot.save("progress_66pct.png")

            # AI progress analysis at 66% with proximity awareness
            progress_analysis = self.analyze_progress_with_proximity("progress_66pct.png", 66, total_distance, proximity_mode)
            if progress_analysis:
                try:
                    content = progress_analysis["choices"][0]["message"]["content"]
                    if "```json" in content:
                        json_str = content.split("```json")[1].split("```")[0].strip()
                    else:
                        json_str = content.strip()

                    progress_data = json.loads(json_str)
                    action = progress_data.get("action", "CONTINUE")
                    alignment_status = progress_data.get("object_alignment_status", "Unknown")
                    reasoning = progress_data.get("reasoning", "No reasoning provided")

                    print(f"AI Progress Analysis (66%) - {proximity_mode}: {action}")
                    print(f"Object alignment: {alignment_status}")
                    print(f"Reasoning: {reasoning}")

                    if action == "STOP":
                        print("🎯 AI detected object overlap at Waypoint 2 - Adding small final adjustment")

                        # Add small 10px drag for close objects to ensure proper alignment
                        if total_distance <= 100:  # For close/moderate objects
                            final_adjustment = 10
                            final_x = current_x + final_adjustment
                            print(f"🔧 Adding {final_adjustment}px final adjustment for close objects")
                            pyautogui.moveTo(final_x, end_y, duration=0.3)
                            print(f"📍 Final adjustment completed: moved to {final_x:.0f}px")

                        pyautogui.mouseUp(button='left')
                        print("✅ SUCCESS: Drag stopped with final adjustment")
                        print(f"📍 Final position: {current_x + (final_adjustment if total_distance <= 100 else 0):.0f}px")
                        return True
                    elif action == "ADJUST_DISTANCE":
                        adjustment = progress_data.get("distance_adjustment", 0)
                        print(f"AI recommends final distance adjustment: {adjustment} pixels")
                        remaining_distance += adjustment
                        end_x += adjustment
                        print(f"Adjusted final distance: {remaining_distance:.0f}px")

                    # Final dynamic distance adjustment based on proximity
                    if "very close" in alignment_status.lower() or "approaching" in alignment_status.lower():
                        remaining_distance *= 0.3  # Reduce remaining distance by 70%
                        print(f"🔄 Objects very close - reducing final distance by 70% to {remaining_distance:.0f}px")
                    elif "close" in alignment_status.lower():
                        remaining_distance *= 0.5  # Reduce remaining distance by 50%
                        print(f"🔄 Objects close - reducing final distance by 50% to {remaining_distance:.0f}px")

                except Exception as e:
                    print(f"Error parsing progress analysis at 66%: {e}")

            time.sleep(0.1)

            # Dynamic Final Position: Calculate based on remaining distance after all adjustments
            final_distance = max(10, remaining_distance)  # Minimum 10px final movement
            final_x = current_x + final_distance

            # Ensure we don't exceed the original planned endpoint
            final_x = min(final_x, end_x)

            print(f"Dynamic Final Position: Moving {final_distance:.0f}px to x={final_x:.0f}")
            pyautogui.moveTo(final_x, end_y, duration=0.6)
            time.sleep(0.1)

            pyautogui.mouseUp(button='left')
            print("Phase 3: Drag operation completed")

            # CRITICAL: Capture screenshot IMMEDIATELY after drag completion
            print("Phase 4: IMMEDIATE screenshot capture (before captcha state changes)")
            import datetime
            timestamp = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]
            print(f"Screenshot timestamp: {timestamp}")

            immediate_screenshot = ImageGrab.grab(bbox=self.captcha_bbox)
            immediate_screenshot.save("captcha_immediate.png")
            print("Immediate verification screenshot saved as 'captcha_immediate.png'")

            # Minimal pause to allow any visual updates
            print("Phase 5: Minimal processing pause (0.1 seconds)")
            time.sleep(0.1)

            # Capture second screenshot to detect state changes
            print("Phase 6: Secondary screenshot capture")
            timestamp2 = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]
            print(f"Secondary screenshot timestamp: {timestamp2}")

            verification_screenshot = ImageGrab.grab(bbox=self.captcha_bbox)
            verification_screenshot.save("captcha_verification.png")
            print("Secondary verification screenshot saved as 'captcha_verification.png'")

            # Final object alignment verification using IMMEDIATE screenshot
            print("Phase 7: Final object alignment verification (using immediate screenshot)")
            immediate_analysis = self.verify_object_alignment("captcha_immediate.png", from_obj, to_obj)

            # Secondary verification using delayed screenshot for comparison
            print("Phase 8: Secondary verification (using delayed screenshot)")
            delayed_analysis = self.verify_object_alignment("captcha_verification.png", from_obj, to_obj)

            # Compare immediate vs delayed results to detect state changes
            if immediate_analysis and delayed_analysis:
                immediate_success = immediate_analysis.get('alignment_success', False)
                delayed_success = delayed_analysis.get('alignment_success', False)

                if immediate_success != delayed_success:
                    print("🚨 CRITICAL: Captcha state changed between screenshots!")
                    print(f"Immediate (correct): {immediate_success}, Delayed (post-change): {delayed_success}")
                    print("Using IMMEDIATE screenshot results as authoritative")
                    alignment_analysis = immediate_analysis
                else:
                    print("✅ Consistent results between immediate and delayed screenshots")
                    alignment_analysis = immediate_analysis
            else:
                alignment_analysis = immediate_analysis or delayed_analysis

            # Enhanced completion logging
            final_position_relative = end_x - self.captcha_bbox[0]
            final_completion_pct = (final_position_relative / captcha_width) * 100

            print("=== DRAG OPERATION SUMMARY ===")
            print(f"Start position: ({start_x}, {start_y})")
            print(f"End position: ({end_x}, {end_y})")
            print(f"Total drag distance: {abs(end_x - start_x)} pixels")
            print(f"Final slider completion percentage: {final_completion_pct:.1f}%")
            print("Target: Object alignment (not slider completion percentage)")
            print(f"Screenshots saved: 'captcha_immediate.png' and 'captcha_verification.png'")
            if immediate_analysis:
                print(f"IMMEDIATE analysis (authoritative): {immediate_analysis}")
            if delayed_analysis:
                print(f"DELAYED analysis (comparison): {delayed_analysis}")
            if alignment_analysis:
                print(f"FINAL alignment verification: {alignment_analysis}")
            print("===============================")

            return True
            
        except Exception as e:
            print(f"Error executing movement: {e}")
            return False
    
    def solve_captcha(self):
        """Main method to solve the captcha"""
        try:
            # Capture the captcha
            image_path = self.capture_screen_area()

            # Analyze with Grok
            analysis = self.analyze_with_grok(image_path)

            if analysis is None:
                print("Failed to get valid response from Grok API")
                return False

            # Execute the solution
            success = self.execute_movement(analysis)

            return success

        except Exception as e:
            print(f"Error solving captcha: {e}")
            return False

# Usage
time.sleep(2)
solver = CaptchaSolver()
#solver.capture_screen_area()
success = solver.solve_captcha()
print(f"Captcha solved: {success}")