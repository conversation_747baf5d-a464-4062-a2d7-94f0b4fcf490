#!/usr/bin/env python3
"""
Automated Distance Measurement Tool with Computer Vision and Auto-Clicking

This tool combines computer vision object detection with automated clicking to measure
distances between objects in screenshots. It integrates with the existing captcha solver
infrastructure for maximum accuracy and reliability.

Features:
- Automatic screenshot capture using existing captcha solver coordinates
- Grok Vision API integration for object detection
- Automated clicking on detected objects using pyautogui
- Precise distance calculation between clicked points
- Full automation with detailed logging

Usage:
    python automated_distance_tool.py [--mode auto|manual] [--objects "object1,object2"]

Examples:
    python automated_distance_tool.py --mode auto --objects "fox,robot"
    python automated_distance_tool.py --mode manual  # Interactive mode
"""

import requests
import json
import base64
import pyautogui
from PIL import ImageGrab
import time
import math
import argparse
from typing import Tuple, Optional, Dict, Any


class AutomatedDistanceTool:
    def __init__(self, auto_find_slide_button=True):
        # Use same API configuration as captcha solver
        self.api_key = "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"
        self.api_url = "https://openrouter.ai/api/v1"

        # Use same captcha bounding box for consistency
        #self.captcha_bbox = (492, 381, 826, 645)  # (left, top, right, bottom)
        #For Demo Website
        self.captcha_bbox = (1401, 490, 1731, 751)  # (left, top, right, bottom)

        # Initialize slide button coordinates
        self.slide_button_absolute = None
        self.slide_button_relative = None

        # Automatically find slide button if requested
        if auto_find_slide_button:
            self.find_slide_button_automatically()
        else:
            # Fallback to manual coordinates if auto-find is disabled
            self.slide_button_absolute = (1442, 714)  # Fallback coordinates

        
        # Store detected coordinates
        self.detected_objects = {}
        self.clicked_points = []

        print("🎯 Automated Distance Measurement Tool Initialized")
        print(f"📍 Using captcha region: {self.captcha_bbox}")
        if self.slide_button_absolute:
            print(f"🎚️  Slide button reference: {self.slide_button_absolute}")
        else:
            print("⚠️  Slide button not found - some features may be limited")

    def capture_screenshot(self) -> str:
        """Capture screenshot of the captcha area with debugging info"""
        screenshot = ImageGrab.grab(bbox=self.captcha_bbox)
        screenshot.save("distance_measurement.png")
        
        # Add debugging info
        print(f"📸 Screenshot captured: {self.captcha_bbox}")
        print(f"📏 Screenshot size: {screenshot.size}")
        
        # Calculate average brightness for debugging
        try:
            import numpy as np
            img_array = np.array(screenshot)
            avg_brightness = np.mean(img_array)
            print(f"💡 Screenshot average brightness: {avg_brightness:.1f} (0=black, 255=white)")
        except ImportError:
            print("NumPy not available - skipping brightness check")
        
        return "distance_measurement.png"

    def detect_objects_with_grok(self, image_path: str, target_objects: str = "two main objects") -> Optional[Dict[str, Any]]:
        """Use Grok Vision API to detect and locate objects in the screenshot"""
        with open(image_path, "rb") as image_file:
            base64_image = base64.b64encode(image_file.read()).decode('utf-8')

        payload = {
            "model": "qwen/qwen2.5-vl-32b-instruct",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": f"""Analyze this image and identify {target_objects} for distance measurement.

CRITICAL REQUIREMENTS FOR OBJECT DETECTION:
- Identify and locate the two most prominent objects in the image
- Measure the precise pixel coordinates of each object's CENTER point
- Look carefully at the actual object boundaries and find the true geometric center
- Consider the object's visual mass distribution when determining center
- The coordinates should be relative to this image (not the full screen)
- Be extremely accurate - even 5-10 pixel errors can affect distance measurement

COORDINATE PRECISION:
- Examine each object's shape and boundaries carefully
- Calculate the center point based on the object's actual dimensions
- Account for any visual effects, shadows, or transparency
- Provide coordinates as precise integers

Return your response in this exact JSON format:
{{
  "objects": [
    {{
      "name": "object1_name",
      "coordinates": {{"x": precise_x_coordinate, "y": precise_y_coordinate}},
      "description": "brief description of the object"
    }},
    {{
      "name": "object2_name", 
      "coordinates": {{"x": precise_x_coordinate, "y": precise_y_coordinate}},
      "description": "brief description of the object"
    }}
  ],
  "image_analysis": "brief description of what you see in the image",
  "confidence": "high|medium|low"
}}

Please provide only the JSON response without any additional text."""
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            "temperature": 0.1
        }

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        try:
            print("🤖 Sending image to Grok Vision API for object detection...")
            response = requests.post(f"{self.api_url}/chat/completions", headers=headers, json=payload)
            
            print(f"📡 Response status: {response.status_code}")
            
            if response.status_code != 200:
                print(f"❌ API Error: {response.status_code}")
                print(f"Response: {response.text}")
                return None

            if not response.text.strip():
                print("❌ Empty response from API")
                return None

            return response.json()

        except Exception as e:
            print(f"❌ Error calling Grok API: {e}")
            return None

    def parse_detection_response(self, response: Dict[str, Any]) -> bool:
        """Parse the Grok API response and extract object coordinates"""
        try:
            content = response["choices"][0]["message"]["content"]
            print(f"🤖 AI Response: {content}")
            
            # Extract JSON from response
            if "```json" in content:
                json_str = content.split("```json")[1].split("```")[0].strip()
            else:
                json_str = content.strip()
            
            data = json.loads(json_str)
            
            # Validate response structure
            if "objects" not in data or len(data["objects"]) < 2:
                print("❌ Invalid response: Need at least 2 objects")
                return False
            
            # Extract object coordinates
            self.detected_objects = {}
            for i, obj in enumerate(data["objects"][:2]):  # Take first 2 objects
                name = obj.get("name", f"object_{i+1}")
                coords = obj.get("coordinates", {})
                description = obj.get("description", "unknown object")
                
                if "x" not in coords or "y" not in coords:
                    print(f"❌ Missing coordinates for {name}")
                    return False
                
                self.detected_objects[name] = {
                    "coordinates": (coords["x"], coords["y"]),
                    "description": description
                }
                
                print(f"✅ Detected {name}: {coords} - {description}")
            
            confidence = data.get("confidence", "unknown")
            image_analysis = data.get("image_analysis", "No analysis provided")
            
            print(f"🎯 Detection confidence: {confidence}")
            print(f"📋 Image analysis: {image_analysis}")
            
            return True
            
        except json.JSONDecodeError as e:
            print(f"❌ JSON decode error: {e}")
            print(f"Response content: {content}")
            return False
        except Exception as e:
            print(f"❌ Error parsing response: {e}")
            return False

    def convert_to_absolute_coordinates(self, relative_coords: Tuple[int, int]) -> Tuple[int, int]:
        """Convert relative image coordinates to absolute screen coordinates"""
        rel_x, rel_y = relative_coords
        captcha_left, captcha_top = self.captcha_bbox[0], self.captcha_bbox[1]

        abs_x = captcha_left + rel_x
        abs_y = captcha_top + rel_y

        print(f"🔄 Coordinate conversion: ({rel_x}, {rel_y}) → ({abs_x}, {abs_y})")
        return (abs_x, abs_y)

    def get_slide_button_reference(self) -> Tuple[int, int]:
        """Get the slide button absolute coordinates as reference point"""
        return self.slide_button_absolute

    def calculate_distance_from_slide_button(self, target_coords: Tuple[int, int]) -> float:
        """Calculate distance from slide button to target coordinates"""
        slide_x, slide_y = self.slide_button_absolute
        target_x, target_y = target_coords

        distance = math.sqrt((target_x - slide_x)**2 + (target_y - slide_y)**2)

        print(f"📏 Distance from slide button {self.slide_button_absolute} to {target_coords}: {distance:.2f} pixels")
        return distance

    def get_relative_position_to_slide_button(self, coords: Tuple[int, int]) -> Tuple[int, int]:
        """Get position relative to slide button (useful for drag calculations)"""
        slide_x, slide_y = self.slide_button_absolute
        coord_x, coord_y = coords

        relative_x = coord_x - slide_x
        relative_y = coord_y - slide_y

        print(f"📍 Position relative to slide button: ({relative_x}, {relative_y})")
        return (relative_x, relative_y)

    def click_on_objects(self, validate: bool = True, safe_mode: bool = True) -> bool:
        """Automatically click on the detected objects with safety features"""
        if len(self.detected_objects) < 2:
            print("❌ Need at least 2 detected objects to click")
            return False

        # Validate coordinates if requested
        if validate and not self.validate_detection_accuracy():
            print("❌ Detection validation failed - aborting clicks for safety")
            return False

        self.clicked_points = []

        print("🖱️  Starting automated clicking sequence...")

        # Safety check: ensure pyautogui failsafe is enabled
        if safe_mode:
            pyautogui.FAILSAFE = True
            print("🛡️  PyAutoGUI failsafe enabled (move mouse to top-left corner to abort)")

        for i, (name, obj_data) in enumerate(self.detected_objects.items()):
            if i >= 2:  # Only click on first 2 objects
                break

            rel_coords = obj_data["coordinates"]
            abs_coords = self.convert_to_absolute_coordinates(rel_coords)
            description = obj_data["description"]

            print(f"🎯 Clicking on {name} ({description}) at {abs_coords}")

            # Safety check: ensure coordinates are reasonable
            screen_width, screen_height = pyautogui.size()
            if not (0 <= abs_coords[0] <= screen_width and 0 <= abs_coords[1] <= screen_height):
                print(f"❌ Unsafe coordinates {abs_coords} - outside screen bounds")
                return False

            try:
                # Move to position and click with smooth movement
                pyautogui.moveTo(abs_coords[0], abs_coords[1], duration=0.5)
                time.sleep(0.2)

                # Gentle click
                pyautogui.click(button='left')
                time.sleep(0.3)

                self.clicked_points.append(abs_coords)
                print(f"✅ Successfully clicked on {name}")

            except pyautogui.FailSafeException:
                print("🛑 PyAutoGUI failsafe triggered - stopping clicks")
                return False
            except Exception as e:
                print(f"❌ Error clicking on {name}: {e}")
                return False

        print(f"🎉 Completed clicking sequence: {len(self.clicked_points)} points clicked")
        return True

    def calculate_distance(self) -> Optional[float]:
        """Calculate the pixel distance between the two clicked points with slide button reference"""
        if len(self.clicked_points) < 2:
            print("❌ Need at least 2 clicked points to calculate distance")
            return None

        point1, point2 = self.clicked_points[0], self.clicked_points[1]

        # Calculate Euclidean distance between points
        distance = math.sqrt((point2[0] - point1[0])**2 + (point2[1] - point1[1])**2)

        # Calculate distances from slide button for reference
        distance_to_slide_1 = self.calculate_distance_from_slide_button(point1)
        distance_to_slide_2 = self.calculate_distance_from_slide_button(point2)

        # Get relative positions to slide button
        rel_pos_1 = self.get_relative_position_to_slide_button(point1)
        rel_pos_2 = self.get_relative_position_to_slide_button(point2)

        print("📏 COMPREHENSIVE DISTANCE ANALYSIS:")
        print(f"   🎚️  Slide button reference: {self.slide_button_absolute}")
        print(f"   📍 Point 1: {point1} (relative to slide: {rel_pos_1})")
        print(f"   📍 Point 2: {point2} (relative to slide: {rel_pos_2})")
        print(f"   📏 Distance between points: {distance:.2f} pixels")
        print(f"   📐 Horizontal gap: {abs(point2[0] - point1[0])} pixels")
        print(f"   📐 Vertical gap: {abs(point2[1] - point1[1])} pixels")
        print(f"   🎚️  Distance from slide to Point 1: {distance_to_slide_1:.2f} pixels")
        print(f"   🎚️  Distance from slide to Point 2: {distance_to_slide_2:.2f} pixels")

        # Calculate drag distance if objects were to be aligned via slide button
        drag_distance = abs(point2[0] - point1[0])  # Horizontal drag distance
        print(f"   🖱️  Required drag distance for alignment: {drag_distance:.2f} pixels")

        return distance

    def run_automated_measurement(self, target_objects: str = "two main objects") -> Optional[float]:
        """Run the complete automated distance measurement process"""
        print("🚀 Starting Automated Distance Measurement Process")
        print("=" * 60)
        
        # Step 1: Capture screenshot
        print("📸 Step 1: Capturing screenshot...")
        image_path = self.capture_screenshot()
        
        # Step 2: Detect objects with AI
        print("🤖 Step 2: Detecting objects with Grok Vision...")
        response = self.detect_objects_with_grok(image_path, target_objects)
        if not response:
            print("❌ Failed to get AI response")
            return None
        
        # Step 3: Parse detection results
        print("📋 Step 3: Parsing detection results...")
        if not self.parse_detection_response(response):
            print("❌ Failed to parse detection results")
            return None
        
        # Step 4: Click on detected objects
        print("🖱️  Step 4: Clicking on detected objects...")
        if not self.click_on_objects():
            print("❌ Failed to click on objects")
            return None
        
        # Step 5: Calculate distance
        print("📏 Step 5: Calculating distance...")
        distance = self.calculate_distance()
        
        if distance is not None:
            print("=" * 60)
            print("🎉 AUTOMATED MEASUREMENT COMPLETED SUCCESSFULLY!")
            print(f"📏 Final Distance: {distance:.2f} pixels")
            print("=" * 60)
        
        return distance


    def run_continuous_monitoring(self, target_objects: str = "two main objects", interval: int = 5) -> None:
        """Run continuous distance monitoring with specified interval"""
        print(f"🔄 Starting continuous monitoring (every {interval} seconds)")
        print("Press Ctrl+C to stop monitoring")

        try:
            measurement_count = 0
            while True:
                measurement_count += 1
                print(f"\n📊 Measurement #{measurement_count} - {time.strftime('%H:%M:%S')}")

                distance = self.run_automated_measurement(target_objects)

                if distance is not None:
                    print(f"✅ Measurement #{measurement_count}: {distance:.2f} pixels")
                else:
                    print(f"❌ Measurement #{measurement_count}: Failed")

                print(f"⏳ Waiting {interval} seconds for next measurement...")
                time.sleep(interval)

        except KeyboardInterrupt:
            print(f"\n🛑 Monitoring stopped. Completed {measurement_count} measurements.")

    def save_measurement_log(self, distance: float, filename: str = "distance_log.txt") -> None:
        """Save measurement results to a log file"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"{timestamp} - Distance: {distance:.2f} pixels - Objects: {list(self.detected_objects.keys())}\n"

        try:
            with open(filename, "a") as f:
                f.write(log_entry)
            print(f"📝 Measurement logged to {filename}")
        except Exception as e:
            print(f"❌ Failed to save log: {e}")

    def validate_detection_accuracy(self) -> bool:
        """Validate that detected coordinates are within reasonable bounds"""
        captcha_width = self.captcha_bbox[2] - self.captcha_bbox[0]
        captcha_height = self.captcha_bbox[3] - self.captcha_bbox[1]

        for name, obj_data in self.detected_objects.items():
            x, y = obj_data["coordinates"]

            # Check if coordinates are within captcha bounds
            if x < 0 or x >= captcha_width or y < 0 or y >= captcha_height:
                print(f"⚠️  Warning: {name} coordinates ({x}, {y}) are outside captcha bounds")
                return False

            # Check if coordinates are not at extreme edges (likely detection errors)
            margin = 10
            if x < margin or x > captcha_width - margin or y < margin or y > captcha_height - margin:
                print(f"⚠️  Warning: {name} coordinates ({x}, {y}) are very close to edges")

        print("✅ Detection coordinates validation passed")
        return True

    def integrate_with_captcha_solver(self) -> Optional[Dict[str, Any]]:
        """Integration method for use with existing captcha solver - returns comprehensive data"""
        print("🔗 Integration mode: Using captcha solver infrastructure")
        print(f"🎚️  Using slide button reference: {self.slide_button_absolute}")

        # Use the same screenshot capture method as captcha solver
        image_path = self.capture_screenshot()

        # Detect objects using the same AI model
        response = self.detect_objects_with_grok(image_path, "fox and robot objects")
        if not response:
            return None

        # Parse and validate
        if not self.parse_detection_response(response):
            return None

        # Calculate comprehensive measurements for captcha solver use
        if len(self.detected_objects) >= 2:
            objects = list(self.detected_objects.values())
            coord1_rel = objects[0]["coordinates"]  # Relative to captcha
            coord2_rel = objects[1]["coordinates"]  # Relative to captcha

            # Convert to absolute coordinates
            coord1_abs = self.convert_to_absolute_coordinates(coord1_rel)
            coord2_abs = self.convert_to_absolute_coordinates(coord2_rel)

            # Calculate distance between objects
            distance = math.sqrt((coord2_rel[0] - coord1_rel[0])**2 + (coord2_rel[1] - coord1_rel[1])**2)

            # Calculate slide button relationships
            slide_to_obj1 = self.calculate_distance_from_slide_button(coord1_abs)
            slide_to_obj2 = self.calculate_distance_from_slide_button(coord2_abs)

            # Calculate drag distance needed for alignment
            drag_distance = abs(coord2_abs[0] - coord1_abs[0])  # Horizontal drag

            # Determine drag direction
            drag_direction = "right" if coord2_abs[0] > coord1_abs[0] else "left"

            # Create comprehensive result for captcha solver
            result = {
                "distance_pixels": round(distance, 2),
                "object1": {
                    "relative_coords": coord1_rel,
                    "absolute_coords": coord1_abs,
                    "distance_from_slide": round(slide_to_obj1, 2),
                    "name": list(self.detected_objects.keys())[0]
                },
                "object2": {
                    "relative_coords": coord2_rel,
                    "absolute_coords": coord2_abs,
                    "distance_from_slide": round(slide_to_obj2, 2),
                    "name": list(self.detected_objects.keys())[1]
                },
                "slide_button": self.slide_button_absolute,
                "drag_info": {
                    "distance": round(drag_distance, 2),
                    "direction": drag_direction,
                    "start_point": coord1_abs,
                    "target_point": coord2_abs
                },
                "captcha_region": self.captcha_bbox
            }

            print("📏 CAPTCHA SOLVER INTEGRATION RESULTS:")
            print(f"   📍 Object distance: {distance:.2f} pixels")
            print(f"   🎚️  Slide button: {self.slide_button_absolute}")
            print(f"   🖱️  Required drag: {drag_distance:.2f} pixels {drag_direction}")
            print(f"   📊 Object 1 ({result['object1']['name']}): {coord1_abs}")
            print(f"   📊 Object 2 ({result['object2']['name']}): {coord2_abs}")

            return result

        return None

    def get_captcha_solver_measurements(self) -> Optional[Dict[str, float]]:
        """Get measurements in format compatible with captcha solver distance calculations"""
        integration_result = self.integrate_with_captcha_solver()

        if integration_result:
            return {
                "total_distance": integration_result["distance_pixels"],
                "drag_distance": integration_result["drag_info"]["distance"],
                "object1_from_slide": integration_result["object1"]["distance_from_slide"],
                "object2_from_slide": integration_result["object2"]["distance_from_slide"],
                "horizontal_gap": abs(integration_result["object2"]["absolute_coords"][0] -
                                    integration_result["object1"]["absolute_coords"][0]),
                "vertical_gap": abs(integration_result["object2"]["absolute_coords"][1] -
                                  integration_result["object1"]["absolute_coords"][1])
            }

        return None

    def create_measurement_report(self, distance: float) -> Dict[str, Any]:
        """Create a detailed measurement report with slide button reference"""
        report = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "distance_pixels": round(distance, 2),
            "objects_detected": len(self.detected_objects),
            "object_details": {},
            "slide_button_reference": self.slide_button_absolute,
            "captcha_region": self.captcha_bbox,
            "clicked_points": self.clicked_points,
            "slide_button_analysis": {}
        }

        # Add object details with slide button relationships
        for name, obj_data in self.detected_objects.items():
            abs_coords = self.convert_to_absolute_coordinates(obj_data["coordinates"])
            distance_from_slide = self.calculate_distance_from_slide_button(abs_coords)
            relative_to_slide = self.get_relative_position_to_slide_button(abs_coords)

            report["object_details"][name] = {
                "coordinates": obj_data["coordinates"],
                "description": obj_data["description"],
                "absolute_coordinates": abs_coords,
                "distance_from_slide_button": round(distance_from_slide, 2),
                "relative_to_slide_button": relative_to_slide
            }

        # Add slide button analysis if we have clicked points
        if len(self.clicked_points) >= 2:
            drag_distance = abs(self.clicked_points[1][0] - self.clicked_points[0][0])
            drag_direction = "right" if self.clicked_points[1][0] > self.clicked_points[0][0] else "left"

            report["slide_button_analysis"] = {
                "required_drag_distance": round(drag_distance, 2),
                "drag_direction": drag_direction,
                "start_relative_to_slide": self.get_relative_position_to_slide_button(self.clicked_points[0]),
                "end_relative_to_slide": self.get_relative_position_to_slide_button(self.clicked_points[1])
            }

        return report


def main():
    parser = argparse.ArgumentParser(description="Automated Distance Measurement Tool")
    parser.add_argument("--mode", choices=["auto", "manual", "monitor"], default="auto",
                       help="Measurement mode: auto (single measurement), manual (interactive), monitor (continuous)")
    parser.add_argument("--objects", type=str, default="two main objects",
                       help="Description of objects to detect (e.g., 'fox,robot' or 'two main objects')")
    parser.add_argument("--interval", type=int, default=5,
                       help="Interval in seconds for continuous monitoring mode")
    parser.add_argument("--log", action="store_true",
                       help="Save measurement results to log file")
    parser.add_argument("--validate", action="store_true", default=True,
                       help="Validate detection accuracy before clicking")

    args = parser.parse_args()

    # Initialize the tool
    tool = AutomatedDistanceTool()

    if args.mode == "auto":
        # Run single automated measurement
        print(f"🎯 Target objects: {args.objects}")
        distance = tool.run_automated_measurement(args.objects)

        if distance is not None:
            print(f"\n✅ SUCCESS: Distance measured = {distance:.2f} pixels")

            # Save to log if requested
            if args.log:
                tool.save_measurement_log(distance)
        else:
            print("\n❌ FAILED: Could not complete automated measurement")

    elif args.mode == "monitor":
        # Run continuous monitoring
        print(f"🎯 Target objects: {args.objects}")
        tool.run_continuous_monitoring(args.objects, args.interval)

    else:
        # Manual/interactive mode (future enhancement)
        print("🔧 Manual mode not yet implemented")
        print("Use --mode auto for single measurement or --mode monitor for continuous monitoring")


if __name__ == "__main__":
    main()
