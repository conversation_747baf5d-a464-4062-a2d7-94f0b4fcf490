#!/usr/bin/env python3
"""
Distance Measurement on Screenshots (OpenCV)

Usage examples:
---------------
1) Basic pixel distance (two points):
   python distance_from_screenshot.py --image path/to/screenshot.png --mode points

2) Two rectangles (drag each with mouse):
   python distance_from_screenshot.py --image path/to/screenshot.png --mode rects --rect-metric center

3) Real-world conversion using a known scale drawn on the image:
   - First, click two points spanning a known real length (e.g., a UI element known to be 50 mm wide)
   - Enter that real length when prompted (e.g., "50")
   python distance_from_screenshot.py --image path/to/screenshot.png --mode points --calibrate

4) Perspective correction (if objects lie on a planar surface and you can outline a rectangle of known WxH units):
   - First, you'll click 4 corners of a reference rectangle (top-left -> top-right -> bottom-right -> bottom-left).
   - Provide its real width and height (in any unit). The image will be rectified to top-down view.
   - Then measure distances in rectified view with either --mode points or --mode rects.
   python distance_from_screenshot.py --image path/to/photo.png --mode points --perspective --ref-size 210 297

Notes & Tips:
-------------
- Without a scale (calibration), you can only get distance in pixels.
- With a scale, you can convert pixels to your chosen units (mm, cm, in, etc.).
- Perspective correction is only valid when the measurement and the reference rectangle are on the same plane.
- For screenshots of UIs, you may also convert using DPI: pixels_per_unit = DPI / units_per_inch.
  However, UI zoom/scaling often breaks strict DPI assumptions, so on-image scale is recommended for accuracy.

Dependencies:
-------------
pip install opencv-python numpy

"""
import argparse
import math
import sys
from dataclasses import dataclass
from typing import List, Tuple, Optional

import cv2
import numpy as np


# ------------------------------
# Data containers
# ------------------------------

@dataclass
class ClickState:
    points: List[Tuple[int, int]]
    rects: List[Tuple[int, int, int, int]]  # x, y, w, h
    dragging: bool = False
    start_pt: Optional[Tuple[int, int]] = None
    current_rect: Optional[Tuple[int, int, int, int]] = None


# ------------------------------
# Geometry helpers
# ------------------------------

def euclidean(p1: Tuple[float, float], p2: Tuple[float, float]) -> float:
    return float(math.hypot(p1[0] - p2[0], p1[1] - p2[1]))


def rect_center(r: Tuple[int, int, int, int]) -> Tuple[float, float]:
    x, y, w, h = r
    return (x + w / 2.0, y + h / 2.0)


def rect_edges(r: Tuple[int, int, int, int]) -> Tuple[Tuple[int, int], Tuple[int, int], Tuple[int, int], Tuple[int, int]]:
    x, y, w, h = r
    return ( (x, y), (x + w, y), (x + w, y + h), (x, y + h) )


def closest_edge_distance(r1, r2) -> float:
    # Approximate as minimum distance between edges (perimeter sampling)
    # For accuracy we sample points along each edge.
    def sample_rect_points(r, n=200):
        x, y, w, h = r
        pts = []
        # top edge
        for i in range(n):
            t = i / (n - 1)
            pts.append((x + int(t * w), y))
        # right
        for i in range(n):
            t = i / (n - 1)
            pts.append((x + w, y + int(t * h)))
        # bottom
        for i in range(n):
            t = i / (n - 1)
            pts.append((x + int(t * w), y + h))
        # left
        for i in range(n):
            t = i / (n - 1)
            pts.append((x, y + int(t * h)))
        return pts

    pts1 = sample_rect_points(r1)
    pts2 = sample_rect_points(r2)
    mind = float("inf")
    for a in pts1:
        for b in pts2:
            d = euclidean(a, b)
            if d < mind:
                mind = d
    return mind


# ------------------------------
# Perspective utilities
# ------------------------------

def order_quad(pts: List[Tuple[int, int]]) -> np.ndarray:
    pts = np.array(pts, dtype=np.float32)
    # Sum and diff
    s = pts.sum(axis=1)
    d = np.diff(pts, axis=1).reshape(-1)
    ordered = np.zeros((4, 2), dtype=np.float32)
    ordered[0] = pts[np.argmin(s)]  # top-left
    ordered[2] = pts[np.argmax(s)]  # bottom-right
    ordered[1] = pts[np.argmin(d)]  # top-right
    ordered[3] = pts[np.argmax(d)]  # bottom-left
    return ordered


def four_point_transform(image: np.ndarray, pts: List[Tuple[int, int]], out_size: Tuple[int, int]) -> np.ndarray:
    src = order_quad(pts)
    W, H = out_size
    dst = np.array([[0, 0], [W - 1, 0], [W - 1, H - 1], [0, H - 1]], dtype=np.float32)
    M = cv2.getPerspectiveTransform(src, dst)
    warped = cv2.warpPerspective(image, M, (W, H))
    return warped


# ------------------------------
# Calibration helpers
# ------------------------------

@dataclass
class Calibration:
    px_per_unit: Optional[float] = None
    unit_name: str = "units"  # e.g., "mm", "cm", "in"


def prompt_float(msg: str) -> Optional[float]:
    try:
        v = float(input(msg))
        if v <= 0:
            return None
        return v
    except Exception:
        return None


def compute_scale_from_two_points(p1: Tuple[int, int], p2: Tuple[int, int]) -> Optional[Calibration]:
    px_len = euclidean(p1, p2)
    real_len = prompt_float("Enter the REAL length between the two clicked points (e.g., 50): ")
    if real_len is None:
        print("Invalid real length. Skipping calibration.")
        return None
    unit = input("Enter unit name (e.g., mm, cm, in) [default mm]: ").strip() or "mm"
    px_per_unit = px_len / real_len
    print(f"Calibrated: {px_per_unit:.6f} pixels per {unit}.")
    return Calibration(px_per_unit=px_per_unit, unit_name=unit)


def px_to_units(px: float, calib: Optional[Calibration]) -> str:
    if calib and calib.px_per_unit and calib.px_per_unit > 0:
        val = px / calib.px_per_unit
        return f"{val:.3f} {calib.unit_name}"
    else:
        return f"{px:.2f} px"


# ------------------------------
# Mouse interaction
# ------------------------------

def make_points_handler(state: ClickState, window_name: str):
    def handler(event, x, y, flags, param):
        if event == cv2.EVENT_LBUTTONDOWN:
            state.points.append((x, y))
    return handler


def make_rects_handler(state: ClickState, window_name: str):
    def handler(event, x, y, flags, param):
        if event == cv2.EVENT_LBUTTONDOWN:
            state.dragging = True
            state.start_pt = (x, y)
            state.current_rect = None
        elif event == cv2.EVENT_MOUSEMOVE and state.dragging:
            sx, sy = state.start_pt
            w = x - sx
            h = y - sy
            x0 = sx if w >= 0 else x
            y0 = sy if h >= 0 else y
            state.current_rect = (x0, y0, abs(w), abs(h))
        elif event == cv2.EVENT_LBUTTONUP:
            state.dragging = False
            if state.current_rect and state.current_rect[2] > 0 and state.current_rect[3] > 0:
                state.rects.append(state.current_rect)
            state.current_rect = None
    return handler


# ------------------------------
# Drawing helpers
# ------------------------------

def draw_points(img: np.ndarray, pts: List[Tuple[int, int]]):
    for i, (x, y) in enumerate(pts):
        cv2.circle(img, (x, y), 4, (0, 0, 255), -1)
        cv2.putText(img, f"P{i+1}", (x + 6, y - 6), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 3, cv2.LINE_AA)
        cv2.putText(img, f"P{i+1}", (x + 6, y - 6), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1, cv2.LINE_AA)


def draw_rects(img: np.ndarray, rects: List[Tuple[int, int, int, int]]):
    for i, (x, y, w, h) in enumerate(rects):
        cv2.rectangle(img, (x, y), (x + w, y + h), (0, 255, 0), 2)
        cx, cy = rect_center((x, y, w, h))
        cv2.circle(img, (int(cx), int(cy)), 3, (255, 0, 0), -1)
        cv2.putText(img, f"R{i+1}", (x, y - 8), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 3, cv2.LINE_AA)
        cv2.putText(img, f"R{i+1}", (x, y - 8), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1, cv2.LINE_AA)


def overlay_text(img: np.ndarray, text: str, y: int = 24):
    cv2.putText(img, text, (12, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 3, cv2.LINE_AA)
    cv2.putText(img, text, (12, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 1, cv2.LINE_AA)


# ------------------------------
# Main logic
# ------------------------------

def main():
    parser = argparse.ArgumentParser(description="Measure distances on screenshots/photos with optional calibration and perspective correction.")
    parser.add_argument("--image", required=True, help="Path to input image (screenshot/photo).")
    parser.add_argument("--mode", choices=["points", "rects"], default="points", help="Measurement mode.")
    parser.add_argument("--rect-metric", choices=["center", "edge"], default="center", help="For rects: distance metric between rectangles (center-to-center or closest-edge).")
    parser.add_argument("--calibrate", action="store_true", help="Calibrate pixel-to-units by clicking two points spanning a known real length.")
    parser.add_argument("--perspective", action="store_true", help="Enable perspective correction by clicking 4 corners of a known rectangle on the same plane.")
    parser.add_argument("--ref-size", nargs=2, type=float, metavar=("REF_W", "REF_H"),
                        help="Real width and height (in arbitrary units) of the reference rectangle for perspective correction, e.g., '210 297' for A4 mm.")
    args = parser.parse_args()

    img = cv2.imread(args.image, cv2.IMREAD_COLOR)
    if img is None:
        print(f"Failed to read image: {args.image}")
        sys.exit(1)

    working = img.copy()
    calib: Optional[Calibration] = None

    # ---- Perspective correction flow ----
    if args.perspective:
        if not args.ref_size:
            print("--perspective requires --ref-size W H (real units).")
            sys.exit(1)

        refW, refH = args.ref_size
        print("Perspective mode: Click 4 corners of the reference rectangle in order: TL -> TR -> BR -> BL")
        state = ClickState(points=[], rects=[])
        win = "Perspective Ref (press ENTER when done)"
        cv2.namedWindow(win, cv2.WINDOW_NORMAL | cv2.WINDOW_KEEPRATIO)
        cv2.setMouseCallback(win, make_points_handler(state, win))

        while True:
            disp = working.copy()
            overlay_text(disp, "Click TL -> TR -> BR -> BL of reference rectangle. Press ENTER to warp.")
            draw_points(disp, state.points)
            cv2.imshow(win, disp)
            key = cv2.waitKey(16) & 0xFF
            if key == 13:  # ENTER
                if len(state.points) != 4:
                    print("Need exactly 4 points.")
                    continue
                break
            elif key == 27:
                print("Cancelled.")
                sys.exit(0)

        # Map to an aspect-matching canvas so that 1 px corresponds to a fixed unit scale
        # We choose an arbitrary pixels-per-unit to keep resolution nice:
        px_per_unit = 3.0  # increase for higher res
        outW = int(refW * px_per_unit)
        outH = int(refH * px_per_unit)
        warped = four_point_transform(working, state.points, (outW, outH))

        # After warping, 1 'unit' corresponds to px_per_unit pixels.
        calib = Calibration(px_per_unit=px_per_unit, unit_name="ref_units")
        working = warped  # Continue measurement on rectified image
        print(f"Perspective rectified. Scale is {px_per_unit} px per ref_units.")

    # ---- Optional on-image calibration (independent of perspective) ----
    if args.calibrate:
        print("Calibration: Click two points spanning a known real length, then press ENTER.")
        state = ClickState(points=[], rects=[])
        win = "Calibration (press ENTER)"
        cv2.namedWindow(win, cv2.WINDOW_NORMAL | cv2.WINDOW_KEEPRATIO)
        cv2.setMouseCallback(win, make_points_handler(state, win))
        while True:
            disp = working.copy()
            overlay_text(disp, "Click 2 points for known real length. Press ENTER when done.")
            draw_points(disp, state.points)
            cv2.imshow(win, disp)
            key = cv2.waitKey(16) & 0xFF
            if key == 13:  # ENTER
                if len(state.points) != 2:
                    print("Need exactly 2 points for calibration.")
                    continue
                break
            elif key == 27:
                print("Calibration cancelled.")
                state.points = []
                break
        cv2.destroyWindow(win)
        if len(state.points) == 2:
            c = compute_scale_from_two_points(state.points[0], state.points[1])
            if c:
                calib = c

    # ---- Measurement ----
    if args.mode == "points":
        print("Measurement mode: POINTS. Click two points to measure, press ENTER to compute, ESC to quit.")
        state = ClickState(points=[], rects=[])
        win = "Measure: Points"
        cv2.namedWindow(win, cv2.WINDOW_NORMAL | cv2.WINDOW_KEEPRATIO)
        cv2.setMouseCallback(win, make_points_handler(state, win))

        while True:
            disp = working.copy()
            overlay_text(disp, "Click 2 points. ENTER=measure, ESC=quit.")
            draw_points(disp, state.points)
            cv2.imshow(win, disp)
            key = cv2.waitKey(16) & 0xFF
            if key == 13:  # ENTER
                if len(state.points) >= 2:
                    p1, p2 = state.points[-2], state.points[-1]
                    d_px = euclidean(p1, p2)
                    msg = f"Distance: {px_to_units(d_px, calib)}"
                    print(msg)
                    # Draw annotation
                    disp = working.copy()
                    cv2.line(disp, p1, p2, (0, 255, 255), 2)
                    overlay_text(disp, msg, y=48)
                    draw_points(disp, [p1, p2])
                    cv2.imshow("Result", disp)
                else:
                    print("Need 2 points.")
            elif key == 27:  # ESC
                break
        cv2.destroyAllWindows()

    else:  # rects
        print("Measurement mode: RECTS. Drag two rectangles. ENTER=measure, ESC=quit.")
        state = ClickState(points=[], rects=[])
        win = "Measure: Rects"
        cv2.namedWindow(win, cv2.WINDOW_NORMAL | cv2.WINDOW_KEEPRATIO)
        cv2.setMouseCallback(win, make_rects_handler(state, win))

        while True:
            disp = working.copy()
            overlay_text(disp, "Drag 2 rectangles. ENTER=measure, ESC=quit.")
            draw_rects(disp, state.rects)
            if state.current_rect:
                x, y, w, h = state.current_rect
                cv2.rectangle(disp, (x, y), (x + w, y + h), (0, 200, 255), 2)
            cv2.imshow(win, disp)
            key = cv2.waitKey(16) & 0xFF
            if key == 13:  # ENTER
                if len(state.rects) >= 2:
                    r1, r2 = state.rects[-2], state.rects[-1]
                    if args.rect_metric == "center":
                        d_px = euclidean(rect_center(r1), rect_center(r2))
                        msg = f"Center-to-center: {px_to_units(d_px, calib)}"
                    else:
                        d_px = closest_edge_distance(r1, r2)
                        msg = f"Closest-edge: {px_to_units(d_px, calib)}"
                    print(msg)
                    disp = working.copy()
                    draw_rects(disp, [r1, r2])
                    overlay_text(disp, msg, y=48)
                    cv2.imshow("Result", disp)
                else:
                    print("Need 2 rectangles.")
            elif key == 27:  # ESC
                break
        cv2.destroyAllWindows()


if __name__ == "__main__":
    main()
