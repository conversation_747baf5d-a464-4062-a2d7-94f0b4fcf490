import pyautogui
from PIL import ImageGrab

# Method 1: Use mouse position to find coordinates
def find_captcha_area():
    print("Move mouse to top-left of captcha and press Enter")
    input()
    x1, y1 = pyautogui.position()
    print(f"Top-left: ({x1}, {y1})")
    
    print("Move mouse to bottom-right of captcha and press Enter")
    input()
    x2, y2 = pyautogui.position()
    print(f"Bottom-right: ({x2}, {y2})")
    
    bbox = (x1, y1, x2, y2)
    print(f"Your bbox: {bbox}")
    return bbox

# Test the area
bbox = find_captcha_area()
test_screenshot = ImageGrab.grab(bbox=bbox)
test_screenshot.show()  # Opens the captured area
