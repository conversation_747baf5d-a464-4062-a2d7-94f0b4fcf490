import pyautogui
from PIL import ImageGrab
import time

def find_slide_button_coordinates():
    """Find the exact coordinates of the slide button in the captcha"""
    
    print("=== Slide Button Coordinate Finder ===")
    print("This will help you find the exact position of the slide button")
    print()
    
    # First, capture the captcha area
    print("Step 1: Define the captcha area")
    print("Move mouse to top-left corner of the ENTIRE captcha and press Enter")
    input("Press Enter when ready...")
    x1, y1 = pyautogui.position()
    print(f"Top-left: ({x1}, {y1})")
    
    print("Move mouse to bottom-right corner of the ENTIRE captcha and press Enter")
    input("Press Enter when ready...")
    x2, y2 = pyautogui.position()
    print(f"Bottom-right: ({x2}, {y2})")
    
    captcha_bbox = (x1, y1, x2, y2)
    print(f"Captcha bbox: {captcha_bbox}")
    print()
    
    # Capture and show the captcha area
    print("Capturing captcha area...")
    captcha_screenshot = ImageGrab.grab(bbox=captcha_bbox)
    captcha_screenshot.save("captcha_area.png")
    captcha_screenshot.show()
    print("Captcha area saved as 'captcha_area.png' and displayed")
    print()
    
    # Now find the slide button
    print("Step 2: Find the slide button")
    print("Look at the displayed captcha image and locate the slide button")
    print("It's usually an arrow (→) or circular button on the left side")
    print("Move your mouse to the CENTER of the slide button and press Enter")
    input("Press Enter when mouse is on slide button center...")
    
    slide_x, slide_y = pyautogui.position()
    print(f"Slide button absolute position: ({slide_x}, {slide_y})")
    
    # Convert to relative coordinates within the captcha
    relative_x = slide_x - x1
    relative_y = slide_y - y1
    print(f"Slide button relative position: ({relative_x}, {relative_y})")
    print()
    
    # Test the coordinates
    print("Step 3: Test the coordinates")
    print("Testing slide button position in 3 seconds...")
    time.sleep(3)
    
    # Move to the slide button position
    pyautogui.moveTo(slide_x, slide_y, duration=0.5)
    print("Mouse moved to slide button position")
    print("Is the mouse cursor on the slide button? (Check visually)")
    
    # Provide results
    print()
    print("=== RESULTS ===")
    print(f"Captcha bbox: {captcha_bbox}")
    print(f"Slide button absolute: ({slide_x}, {slide_y})")
    print(f"Slide button relative: ({relative_x}, {relative_y})")
    print()
    print("Copy these values to your ai.py file:")
    print(f"self.captcha_bbox = {captcha_bbox}")
    print(f"Expected slide button relative coords: ({relative_x}, {relative_y})")
    
    return {
        'captcha_bbox': captcha_bbox,
        'slide_absolute': (slide_x, slide_y),
        'slide_relative': (relative_x, relative_y)
    }

def test_multiple_positions():
    """Test multiple slide button positions for better accuracy"""
    print("\n=== Multiple Position Test ===")
    print("Let's test the slide button position multiple times for accuracy")
    
    positions = []
    for i in range(3):
        print(f"\nTest {i+1}/3:")
        print("Move mouse to slide button center and press Enter")
        input("Press Enter when ready...")
        x, y = pyautogui.position()
        positions.append((x, y))
        print(f"Position {i+1}: ({x}, {y})")
    
    # Calculate average
    avg_x = sum(pos[0] for pos in positions) / len(positions)
    avg_y = sum(pos[1] for pos in positions) / len(positions)
    
    print(f"\nAverage position: ({avg_x:.1f}, {avg_y:.1f})")
    print("Use this average for better accuracy!")
    
    return (avg_x, avg_y)

if __name__ == "__main__":
    try:
        # Main coordinate finding
        results = find_slide_button_coordinates()
        
        # Optional: Test multiple positions
        print("\nWould you like to test multiple positions for better accuracy? (y/n)")
        if input().lower() == 'y':
            avg_pos = test_multiple_positions()
            print(f"Recommended slide button position: {avg_pos}")
        
    except KeyboardInterrupt:
        print("\nScript cancelled by user")
    except Exception as e:
        print(f"Error: {e}")
