#!/usr/bin/env python3
"""
Test Script for Automated Distance Measurement Tool

This script demonstrates how to use the automated distance tool
with various configurations and integration scenarios.
"""

import time
from automated_distance_tool import AutomatedDistanceTool


def test_basic_measurement():
    """Test basic automated distance measurement"""
    print("🧪 TEST 1: Basic Automated Measurement")
    print("=" * 50)
    
    tool = AutomatedDistanceTool()
    
    # Run automated measurement
    distance = tool.run_automated_measurement("fox and robot")
    
    if distance:
        print(f"✅ Test 1 PASSED: Distance = {distance:.2f} pixels")
        
        # Create detailed report
        report = tool.create_measurement_report(distance)
        print("📊 Measurement Report:")
        for key, value in report.items():
            print(f"   {key}: {value}")
    else:
        print("❌ Test 1 FAILED: Could not measure distance")
    
    print("\n")


def test_integration_mode():
    """Test integration with captcha solver infrastructure"""
    print("🧪 TEST 2: Captcha Solver Integration")
    print("=" * 50)
    
    tool = AutomatedDistanceTool()
    
    # Test integration mode (analysis only, no clicking)
    distance = tool.integrate_with_captcha_solver()
    
    if distance:
        print(f"✅ Test 2 PASSED: Integration distance = {distance:.2f} pixels")
    else:
        print("❌ Test 2 FAILED: Integration mode failed")
    
    print("\n")


def test_validation_features():
    """Test coordinate validation and safety features"""
    print("🧪 TEST 3: Validation and Safety Features")
    print("=" * 50)
    
    tool = AutomatedDistanceTool()
    
    # Capture screenshot and detect objects
    image_path = tool.capture_screenshot()
    response = tool.detect_objects_with_grok(image_path, "two main objects")
    
    if response and tool.parse_detection_response(response):
        # Test validation
        is_valid = tool.validate_detection_accuracy()
        print(f"🔍 Coordinate validation: {'PASSED' if is_valid else 'FAILED'}")
        
        # Test coordinate conversion
        for name, obj_data in tool.detected_objects.items():
            rel_coords = obj_data["coordinates"]
            abs_coords = tool.convert_to_absolute_coordinates(rel_coords)
            print(f"📍 {name}: {rel_coords} → {abs_coords}")
        
        print("✅ Test 3 PASSED: Validation features working")
    else:
        print("❌ Test 3 FAILED: Could not detect objects for validation")
    
    print("\n")


def test_measurement_comparison():
    """Compare measurements with different object descriptions"""
    print("🧪 TEST 4: Measurement Comparison")
    print("=" * 50)
    
    tool = AutomatedDistanceTool()
    
    test_descriptions = [
        "fox and robot",
        "two main objects",
        "orange fox and blue robot",
        "animal and machine"
    ]
    
    results = {}
    
    for description in test_descriptions:
        print(f"🎯 Testing with description: '{description}'")
        distance = tool.integrate_with_captcha_solver()  # Use non-clicking mode
        
        if distance:
            results[description] = distance
            print(f"   Result: {distance:.2f} pixels")
        else:
            print("   Result: Failed")
        
        time.sleep(1)  # Brief pause between tests
    
    # Analyze consistency
    if len(results) > 1:
        distances = list(results.values())
        avg_distance = sum(distances) / len(distances)
        max_deviation = max(abs(d - avg_distance) for d in distances)
        
        print(f"\n📊 Consistency Analysis:")
        print(f"   Average distance: {avg_distance:.2f} pixels")
        print(f"   Maximum deviation: {max_deviation:.2f} pixels")
        print(f"   Consistency: {'GOOD' if max_deviation < 10 else 'POOR'}")
        
        if max_deviation < 10:
            print("✅ Test 4 PASSED: Measurements are consistent")
        else:
            print("⚠️  Test 4 WARNING: High measurement variation")
    else:
        print("❌ Test 4 FAILED: Not enough successful measurements")
    
    print("\n")


def run_all_tests():
    """Run all test scenarios"""
    print("🚀 AUTOMATED DISTANCE TOOL - COMPREHENSIVE TESTING")
    print("=" * 60)
    print("⚠️  Make sure the captcha is visible on screen before running tests")
    print("⏳ Starting tests in 3 seconds...")
    time.sleep(3)
    
    try:
        # Run all tests
        test_basic_measurement()
        test_integration_mode()
        test_validation_features()
        test_measurement_comparison()
        
        print("🎉 ALL TESTS COMPLETED!")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite error: {e}")


def interactive_test():
    """Interactive test mode for manual verification"""
    print("🎮 INTERACTIVE TEST MODE")
    print("=" * 40)
    
    tool = AutomatedDistanceTool()
    
    while True:
        print("\nChoose a test:")
        print("1. Basic measurement with clicking")
        print("2. Integration mode (no clicking)")
        print("3. Validation test")
        print("4. Custom object description")
        print("5. Exit")
        
        choice = input("Enter choice (1-5): ").strip()
        
        if choice == "1":
            distance = tool.run_automated_measurement("fox and robot")
            if distance:
                print(f"✅ Distance: {distance:.2f} pixels")
        
        elif choice == "2":
            distance = tool.integrate_with_captcha_solver()
            if distance:
                print(f"✅ Distance: {distance:.2f} pixels")
        
        elif choice == "3":
            image_path = tool.capture_screenshot()
            response = tool.detect_objects_with_grok(image_path)
            if response and tool.parse_detection_response(response):
                tool.validate_detection_accuracy()
        
        elif choice == "4":
            description = input("Enter object description: ").strip()
            distance = tool.integrate_with_captcha_solver()
            if distance:
                print(f"✅ Distance: {distance:.2f} pixels")
        
        elif choice == "5":
            print("👋 Goodbye!")
            break
        
        else:
            print("❌ Invalid choice")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        interactive_test()
    else:
        run_all_tests()
